import pandas as pd
import re
import os
import ast
import urllib.parse
import glob


def is_valid_url(url):
    """检查字符串是否是有效的URL"""
    if not isinstance(url, str):
        return False

    # 简单的URL验证正则表达式，适应Facebook重定向链接
    url_pattern = re.compile(
        r"^(https?://)?(www\.|l\.)?"  # http:// 或 https:// 或 www. 或 l.
        r"[a-zA-Z0-9][-a-zA-Z0-9]*(\.[a-zA-Z0-9][-a-zA-Z0-9]*)+"  # 域名
        r"(/[-a-zA-Z0-9%_.~#+&?=]*)*"  # 路径和查询参数，更宽松的匹配
    )
    return bool(url_pattern.match(url))


def extract_urls_from_text(text):
    """从文本中提取URL列表，处理可能的列表格式"""
    if not isinstance(text, str):
        return []

    # 尝试解析为Python列表
    text = text.strip()
    if text.startswith("[") and text.endswith("]"):
        try:
            # 尝试使用ast.literal_eval安全地解析列表
            urls = ast.literal_eval(text)
            if isinstance(urls, list):
                return urls
        except (SyntaxError, ValueError):
            # 如果解析失败，尝试手动解析
            pass

    # 手动解析列表格式
    if text.startswith("['") and text.endswith("']"):
        # 移除首尾的 [' 和 ']
        text = text[2:-2]
        # 按 ', ' 分割
        return [url.strip() for url in text.split("', '")]

    # 如果不是列表格式，则将整个文本作为单个URL返回
    return [text]


def process_excel_file(file_path, output_path=None):
    """
    处理Excel文件:
    1. 读取文件
    2. 剔除blogger_links为空列表[]的行
    3. 剔除product_links中不是链接的行
    4. 剔除每一行的blogger_links和product_links重复的选项
    """
    print(f"正在处理文件: {file_path}")

    # 确定输出路径
    if output_path is None:
        base_name = os.path.basename(file_path)
        name, ext = os.path.splitext(base_name)
        output_path = os.path.join(os.path.dirname(file_path), f"{name}_processed{ext}")

    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 检查必要的列是否存在
        required_columns = ["blogger_link", "product_link"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: 文件缺少必要的列: {', '.join(missing_columns)}")
            return

        # 原始行数
        original_row_count = len(df)
        print(f"原始数据行数: {original_row_count}")

        # 处理前先填充NaN值为空字符串
        df["blogger_link"] = df["blogger_link"].fillna("")
        df["product_link"] = df["product_link"].fillna("")

        # 1. 剔除blogger_links为空列表[]的行
        valid_blogger_links = []
        for i, row in df.iterrows():
            blogger_links_text = str(row["blogger_link"]).strip()

            # 检查是否为空列表
            is_empty_list = blogger_links_text == "[]" or blogger_links_text == ""

            # 如果不是空列表，则保留
            valid_blogger_links.append(not is_empty_list)

        # 应用过滤
        df = df[valid_blogger_links]
        filtered_row_count_1 = len(df)
        print(f"剔除空blogger_links后的行数: {filtered_row_count_1}")
        print(f"剔除的行数: {original_row_count - filtered_row_count_1}")

        # 2. 剔除product_links中不是链接的行
        valid_product_links = []
        for i, row in df.iterrows():
            product_links_text = str(row["product_link"]).strip()

            # 提取产品链接列表
            product_urls = extract_urls_from_text(product_links_text)

            # 检查是否至少有一个有效链接
            has_valid_url = False
            for url in product_urls:
                if is_valid_url(url):
                    has_valid_url = True
                    break

            valid_product_links.append(has_valid_url)

        # 应用过滤
        df = df[valid_product_links]
        filtered_row_count_2 = len(df)
        print(f"剔除无效product_links后的行数: {filtered_row_count_2}")
        print(f"剔除的行数: {filtered_row_count_1 - filtered_row_count_2}")

        # 3. 剔除每一行的blogger_links和product_links重复的选项
        for i, row in df.iterrows():
            blogger_links_text = str(row["blogger_link"]).strip()
            product_links_text = str(row["product_link"]).strip()

            # 提取链接列表
            blogger_urls = extract_urls_from_text(blogger_links_text)
            product_urls = extract_urls_from_text(product_links_text)

            # 提取基本URL部分进行比较（忽略查询参数）
            def extract_base_url(url):
                # 对于Facebook重定向链接，提取目标URL
                if "l.facebook.com/l.php?u=" in url:
                    # 提取u参数的值
                    match = re.search(r"u=(.*?)(?:&|$)", url)
                    if match:
                        target_url = match.group(1)
                        # URL解码
                        target_url = urllib.parse.unquote(target_url)
                        # 移除fbclid参数
                        target_url = re.sub(r"\?fbclid=.*", "", target_url)
                        target_url = re.sub(r"&fbclid=.*", "", target_url)
                        return target_url
                # 移除查询参数
                return re.sub(r"\?.*", "", url)

            # 提取基本URL进行比较
            blogger_base_urls = [extract_base_url(url) for url in blogger_urls]
            product_base_urls = [extract_base_url(url) for url in product_urls]

            # 移除重复链接（基于基本URL比较）
            unique_product_urls = []
            for j, url in enumerate(product_urls):
                if product_base_urls[j] not in blogger_base_urls:
                    unique_product_urls.append(url)

            # 如果移除重复后产品链接为空，则设置为空字符串
            # 否则，保持原来的格式（列表或单个链接）
            if not unique_product_urls:
                df.at[i, "product_link"] = ""
            elif len(product_urls) != len(unique_product_urls):
                # 如果有链接被移除，更新产品链接
                if len(product_urls) > 1 or product_links_text.startswith("["):
                    # 保持列表格式
                    df.at[i, "product_link"] = str(unique_product_urls)
                else:
                    # 单个链接
                    df.at[i, "product_link"] = (
                        unique_product_urls[0] if unique_product_urls else ""
                    )

        # 保存处理后的文件
        df.to_excel(output_path, index=False)
        print(f"处理完成，结果已保存至: {output_path}")

        return df

    except Exception as e:
        print(f"处理文件时出错: {str(e)}")
        return None


def process_all_excel_files(input_dir, output_dir):
    """
    处理指定目录下的所有Excel文件

    Args:
        input_dir: 输入目录路径
        output_dir: 输出目录路径
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 查找所有xlsx文件
    xlsx_pattern = os.path.join(input_dir, "*.xlsx")
    xlsx_files = glob.glob(xlsx_pattern)

    if not xlsx_files:
        print(f"在目录 {input_dir} 中未找到任何xlsx文件")
        return

    print(f"找到 {len(xlsx_files)} 个xlsx文件")

    # 处理每个文件
    for file_path in xlsx_files:
        # 获取文件名
        base_name = os.path.basename(file_path)
        name, ext = os.path.splitext(base_name)

        # 构建输出文件路径
        output_file_path = os.path.join(output_dir, f"{name}_processed{ext}")

        print(f"\n{'='*50}")
        print(f"处理文件: {base_name}")
        print(f"输出到: {output_file_path}")
        print(f"{'='*50}")

        # 处理文件
        result = process_excel_file(file_path, output_file_path)

        if result is not None:
            print(f"✅ 成功处理: {base_name}")
        else:
            print(f"❌ 处理失败: {base_name}")

    print("\n🎉 批量处理完成！")


if __name__ == "__main__":
    # 设置输入和输出目录
    input_dir = "/Users/<USER>/Desktop/code/anotherme/data/meta_tables/raw"
    output_dir = "/Users/<USER>/Desktop/code/anotherme/data/meta_tables/processed"

    # 处理所有Excel文件
    process_all_excel_files(input_dir, output_dir)
