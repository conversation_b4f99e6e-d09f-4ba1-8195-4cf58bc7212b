import os
import base64
import asyncio
import warnings
import string
import random
import shutil
from langchain_openai import ChatOpenAI
from anotherme.browser_use.browser.profile import (
    BrowserProfile,
)

warnings.filterwarnings(
    "ignore", category=ResourceWarning, message="unclosed transport"
)

api_key = os.getenv("API_KEY")
base_url = os.getenv("API_BASE")


llm_model = ChatOpenAI(
    model="google/gemini-2.5-flash-preview-05-20",
    # model='openai/gpt-4.1-mini',
    # model='qwen/qwen2.5-vl-32b-instruct:free',
    api_key=api_key,
    base_url=base_url,
)


async def switch_to_page(context, url=None):
    for page in context.pages:
        if url and url in page.url:
            await page.bring_to_front()
            await page.wait_for_load_state()
            return page
    page = context.pages[0]
    await page.bring_to_front()
    await page.wait_for_load_state()
    return page


async def _rpa_search_1688_async(image_url, headless=False):
    """Async implementation of the 1688 search function"""
    default_user_data_dir = (
        "/Users/<USER>/Desktop/code/anotherme/recordings/user_data"
    )
    tmp_dir = "./recordings/tmp_user_data_dir"
    characters = string.ascii_letters + string.digits
    random_name = "".join(random.choice(characters) for _ in range(6))
    tmp_user_data_dir = tmp_dir + f"_{random_name}"
    if os.path.exists(tmp_user_data_dir):
        shutil.rmtree(tmp_user_data_dir)
    shutil.copytree(default_user_data_dir, tmp_user_data_dir)

    from anotherme.tools.browser_playwright import BrowserWithOutAgent

    browser_profile = BrowserProfile(
        headless=headless,
        user_data_dir=tmp_user_data_dir,
    )
    browser = BrowserWithOutAgent(browser_profile=browser_profile)

    browser.browser_profile.downloads_dir = tmp_user_data_dir
    os.makedirs(tmp_user_data_dir, exist_ok=True)

    searched_images = []
    try:
        page = await browser.browser_session.get_current_page()
        await page.goto("https://www.1688.com/", timeout=60000)
        # await page.wait_for_load_state()
        # await asyncio.sleep(0.5)

        element = await page.query_selector("#dialog-pulgin-guide > img")

        if element:
            await element.click()

        search_input_selector = "#alisearch-input"
        await page.wait_for_selector(search_input_selector, timeout=10000)

        await page.fill(search_input_selector, image_url)
        await page.wait_for_load_state()

        async with browser.browser_context.expect_page():
            await asyncio.sleep(2)
            await page.click("#alisearch-from > div > div > div.input-button")

        await page.wait_for_load_state()

        page = await switch_to_page(browser.browser_context, "searchbox")

        for i in range(2, 7):
            element = await page.query_selector(
                f'//*[@id="sm-offer-list"]/div[{i}]/div[1]/div[1]/div/a/div'
            )
            style = await element.get_attribute("style")
            url = style.split('url("')[-1].split('")')[0]
            searched_images.append(url)
            print(url)

    except Exception as e:
        print(f"Error during automation: {e}")
    finally:
        # Make sure to close browser even if there's an error
        try:
            await asyncio.sleep(0.2)
            await browser.browser_context.__aexit__(None, None, None)
            shutil.rmtree(tmp_user_data_dir)
        except Exception:
            print("browser close fail.")

    return searched_images


def image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


async def llm_verify(image_url, ref_list):
    user_message = []

    prompt = """Does the product in the first image appear in any other images (exactly the same)?
If it appears in at least one image, answer Yes; otherwise, answer No.
The first image:"""
    user_message.append({"type": "text", "text": prompt})
    user_message.append({"type": "image_url", "image_url": image_url})
    prompt = "The other images:"
    user_message.append({"type": "text", "text": prompt})
    for ref in ref_list:
        user_message.append({"type": "image_url", "image_url": ref})

    response = llm_model.invoke([{"role": "user", "content": user_message}])
    print(response)
    generated_content = response.content.strip()
    print(generated_content)
    return "yes" in generated_content.lower()


async def search_1688(image_url, headless=False):
    searched_images = await _rpa_search_1688_async(image_url, headless)

    result = await llm_verify(image_url, searched_images)
    return result


async def test_vlm():
    image_url1 = "https://cbu01.alicdn.com/img/ibank/O1CN01zz3SBw1ScnJe1RYHA_!!*************-0-cib.jpg"
    image_url2 = "https://cbu01.alicdn.com/img/ibank/O1CN01bmU2Sd1xKhjXQY2n2_!!*************-0-cib.jpg"
    image_url3 = "https://cbu01.alicdn.com/img/ibank/O1CN01sNYoPl1DbDiZqgRSp_!!*************-0-cib.jpg"

    ref_list = [
        "https://cbu01.alicdn.com/O1CN010POZD31ScnKZ16eNd_!!*************-0-cib.290x290.jpg?_=2020",
        "https://cbu01.alicdn.com/O1CN01Fg2RcB1PKz4iw8p3u_!!*************-0-cib.290x290.jpg?_=2020",
        "https://cbu01.alicdn.com/O1CN01eLriYg1rMBLWqGOiJ_!!*************-0-cib.290x290.jpg?_=2020",
        "https://cbu01.alicdn.com/O1CN01GW6Ow81cc0ntmJR2Y_!!*************-0-cib.290x290.jpg?_=2020",
        "https://cbu01.alicdn.com/O1CN01zz3SBw1ScnJe1RYHA_!!*************-0-cib.290x290.jpg?_=2020",
    ]
    for url in [image_url1, image_url2, image_url3]:
        print(url)
        await llm_verify(url, ref_list)


async def concurrency_test(image_url, task_num=32, max_workers=8, headless=False):
    """
    Async version of concurrency test using asyncio for better performance.

    Args:
        image_url: URL of the image to search
        task_num: Number of concurrent tasks to run
        max_workers: Maximum number of concurrent workers (semaphore limit)
        headless: Whether to run browser in headless mode
    """
    # Use semaphore to limit concurrent operations
    semaphore = asyncio.Semaphore(max_workers)
    results = []

    async def run_single_task(task_id):
        """Run a single search task with semaphore control"""
        async with semaphore:
            try:
                print(f"Starting task {task_id}")
                result = await _rpa_search_1688_async(image_url, headless)
                status = "success" if result else "fail"
                print(f"Task {task_id} completed {status}")
                return {"task_id": task_id, "result": result, "status": status}
            except Exception as e:
                print(f"Task {task_id} failed with exception: {e}")
                return {
                    "task_id": task_id,
                    "result": None,
                    "status": "failed",
                    "error": str(e),
                }

    # Create all tasks
    tasks = [run_single_task(i) for i in range(task_num)]

    # Run tasks concurrently and collect results with progress bar
    print(f"Starting {task_num} concurrent tasks with max {max_workers} workers...")

    # Use asyncio.as_completed for real-time progress tracking
    completed_tasks = 0
    async for task in asyncio.as_completed(tasks):
        result = await task
        results.append(result)
        completed_tasks += 1
        print(f"Progress: {completed_tasks}/{task_num} tasks completed")

    # Summary
    successful_tasks = [r for r in results if r["status"] == "success"]
    failed_tasks = [r for r in results if r["status"] == "failed"]

    print("\n=== Concurrency Test Summary ===")
    print(f"Total tasks: {task_num}")
    print(f"Successful: {len(successful_tasks)}")
    print(f"Failed: {len(failed_tasks)}")
    print(f"Success rate: {len(successful_tasks)/task_num*100:.1f}%")

    if failed_tasks:
        print(f"\nFailed task IDs: {[t['task_id'] for t in failed_tasks]}")

    return results


async def main():
    image_url = "https://cbu01.alicdn.com/img/ibank/O1CN01zz3SBw1ScnJe1RYHA_!!*************-0-cib.jpg"

    # Test single search first
    result = await search_1688(image_url, headless=False)
    print(result)

    # Basic async concurrency testing
    # results = await concurrency_test(image_url, task_num=4, max_workers=2, headless=False)

    # test the performance of vlm in this task.
    await test_vlm()


if __name__ == "__main__":
    asyncio.run(main())
