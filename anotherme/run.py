import argparse
import os
import threading

from composio_openai import Action, App
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from openai import OpenAI

from anotherme.agents import CodeAgent, ToolCallingAgent
from anotherme.browser_use.browser_use_agent import BrowserUseAgent
from anotherme.mcp.mcp_agent import MCP_Agent
from anotherme.parallel.parallel_agent import Parallel_Agent

from anotherme.models.models import LiteLLMModel
from anotherme.prompts.managed_agents import (
    api_agent_config,
    browseruse_agent_config,
    mcp_agent_config,
    parallel_agent_config,
)
from anotherme.tools.default_tools import GoogleSearchTool
from anotherme.tools.plan_tool import PlanTool
from anotherme.tools.text_inspector_tool import TextInspectorTool
from anotherme.tools.text_web_browser import (
    ArchiveSearchTool,
    FinderTool,
    FindNextTool,
    PageDownTool,
    PageUpTool,
    SimpleTextBrowser,
    VisitTool,
)
from anotherme.tools.visual_qa import visualizer

AUTHORIZED_IMPORTS = [
    "requests",
    "zipfile",
    "os",
    "pandas",
    "numpy",
    "sympy",
    "json",
    "bs4",
    "pubchempy",
    "xml",
    "yahoo_finance",
    "Bio",
    "sklearn",
    "scipy",
    "pydub",
    "io",
    "PIL",
    "chess",
    "PyPDF2",
    "pptx",
    "torch",
    "datetime",
    "fractions",
    "csv",
]
load_dotenv(override=True)

append_answer_lock = threading.Lock()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("question", type=str, help="")
    parser.add_argument("--model-id", type=str, default="gpt-4o")
    return parser.parse_args()


custom_role_conversions = {"tool-call": "assistant", "tool-response": "user"}

user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"

BROWSER_CONFIG = {
    "viewport_size": 1024 * 5,
    "downloads_folder": "results/downloads_folder",
    "request_kwargs": {
        "headers": {"User-Agent": user_agent},
        "timeout": 300,
    },
    "serpapi_key": os.getenv("SERPAPI_API_KEY"),
}

os.makedirs(f"./{BROWSER_CONFIG['downloads_folder']}", exist_ok=True)


def create_agent(
    model_id="o1",
    browser_log_dir="./recordings/example/browser_logs",
    window_size={"width": 1920, "height": 1080},
    window_position={"width": 0, "height": 0},
    headless=False,
    sub_agents=["search", "browser", "mcp"],
    verbose=2,
    storage_state=None,
):
    model_params = {
        "model_id": model_id,
        "custom_role_conversions": custom_role_conversions,
        "api_base": os.getenv("API_BASE"),
        "api_key": os.getenv("API_KEY"),
        "max_completion_tokens": 8192,
    }
    if model_id == "o1":
        model_params["reasoning_effort"] = "high"
    model = LiteLLMModel(**model_params)

    text_limit = 100000

    managed_agents = []
    if "search" in sub_agents:
        search_browser = SimpleTextBrowser(**BROWSER_CONFIG)
        WEB_TOOLS = [
            GoogleSearchTool(provider="serper"),
            VisitTool(search_browser),
            PageUpTool(search_browser),
            PageDownTool(search_browser),
            FinderTool(search_browser),
            FindNextTool(search_browser),
            ArchiveSearchTool(search_browser),
            TextInspectorTool(model, text_limit),
        ]
        search_api_agent = ToolCallingAgent(
            model=model,
            tools=WEB_TOOLS,
            max_steps=20,
            verbosity_level=verbose,
            planning_interval=4,
            name=api_agent_config["name"],
            description=api_agent_config["description"],
            provide_run_summary=True,
            inputs=api_agent_config["inputs"],
        )
        managed_agents.append(search_api_agent)

    if "browser" in sub_agents:
        browseruse_agent = BrowserUseAgent(
            task="",
            llm=ChatOpenAI(
                # model="gpt-4o",
                model="google/gemini-2.5-flash-preview-05-20",
                api_key=os.getenv("API_KEY"),
                base_url=os.getenv("API_BASE"),
                # openai_proxy=os.getenv("PROXY")
            ),
            name=browseruse_agent_config["name"],
            description=browseruse_agent_config["description"],
            inputs=browseruse_agent_config["inputs"],
            log_dir=browser_log_dir,
            window_size=window_size,
            window_position=window_position,
            headless=headless,
            storage_state=storage_state,
        )
        managed_agents.append(browseruse_agent)

    if "mcp" in sub_agents:
        # composio api setup
        entity_id = "lwl"
        composio_apps = [
            App.GMAIL,
            App.Twitter,
            App.Notion,
            App.Slack,
            App.LinkediN,
        ]
        composio_actions = [Action.NOTION_SEARCH_NOTION_PAGE]
        client = OpenAI(
            api_key=os.getenv("OPENAI_API_KEY"), base_url=os.getenv("OPENAI_API_BASE")
        )
        mcp_api_agent = MCP_Agent(
            client,
            composio_entity_id=entity_id,
            composio_actions=composio_actions,
            composio_apps=composio_apps,
            max_steps=10,
            name=mcp_agent_config["name"],
            description=mcp_agent_config["description"],
            inputs=mcp_agent_config["inputs"],
        )
        managed_agents.append(mcp_api_agent)

    if "parallel" in sub_agents:
        sources = sources = {"browser": browseruse_agent}
        parallel_agent = Parallel_Agent(
            llm=ChatOpenAI(
                model="google/gemini-2.5-flash-preview-05-20",
                api_key=os.getenv("OPENROUTER_API_KEY"),
                base_url=os.getenv("OPENROUTER_API_BASE"),
            ),
            max_steps=10,
            sources=sources,
            name=parallel_agent_config["name"],
            description=parallel_agent_config["description"],
            inputs=parallel_agent_config["inputs"],
        )
        managed_agents.append(parallel_agent)

    manager_agent = CodeAgent(
        model=model,
        tools=[visualizer, TextInspectorTool(model, text_limit)],
        planning_tool=PlanTool(),
        max_steps=12,
        verbosity_level=verbose,
        additional_authorized_imports=AUTHORIZED_IMPORTS,
        planning_interval=4,
        managed_agents=managed_agents,
    )

    return manager_agent


def main():
    args = parse_args()

    agent = create_agent(
        model_id=args.model_id,
    )
    answer = agent.run(args.question)

    print(f"Got this answer: {answer}")


if __name__ == "__main__":
    main()
