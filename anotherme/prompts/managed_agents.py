api_agent_config = {
    "name": "search_api_agent",
    "description": """A fast and efficient team member that uses API-based search to quickly find information from the internet.
    This agent is optimized for speed and can rapidly search through multiple sources simultaneously.
    Best for:
    - Quick information retrieval
    - Fact-checking
    - Getting summaries and basic information
    - Searching across multiple sources at once

    Ask him for all your questions that require browsing the web via search API.
    Provide him as much context as possible, in particular if you need to search on a specific timeframe!
    And don't hesitate to provide him with a complex search task, like finding a difference between two webpages.
    Your request must be a real sentence, not a google search! Like "Find me this information (...)" rather than a few keywords.
    """,
    "inputs": {
        "task": {
            "type": "string",
            "description": "The search task to be performed by the agent",
            "nullable": False,
        }
    },
}

browseruse_agent_config = {
    "name": "browseruse_agent",
    "description": """A powerful interactive team member that can perform real browser actions and handle complex web interactions.
    This agent can actually click, type, navigate, and interact with web elements just like a human user.

    Best for:
    - Interactive tasks requiring actual clicks and form submissions
    - Complex web applications that need real user interaction
    - Tasks requiring multiple steps of navigation
    - Extracting dynamic content that requires JavaScript execution
    - Handling login flows and authentication
    - Working with modern web applications

    Note: While this agent can perform more complex tasks, it may be slower than the API-based search agent due to the need to actually load and interact with web pages.

    Always use natural language requests, such as "Click on the email login button displayed on the page." rather than fragmented commands.
    """,
    "inputs": {
        "task": {
            "type": "string",
            "description": "The task to be performed by the agent, Example: 'Enter username and password and log in' ",
        }
    },
}


mcp_agent_config = {
    "name": "mcp_api_agent",
    "description": """A versatile team member that specializes in API-based integrations with popular productivity and communication platforms.
    This agent is optimized for seamless interaction with multiple services and can handle complex workflows across different platforms.

    Best for:
    - Email management and automation via Gmail API
    - Social media interactions and content management via Twitter API
    - Document and knowledge base operations via Notion API
    - Team communication and collaboration via Slack API
    - Professional networking and content sharing via LinkedIn API

    The agent can:
    - Send and manage emails, create drafts, and organize inbox
    - Post tweets, manage followers, and analyze engagement
    - Create and update Notion pages, databases, and workspaces
    - Send messages, create channels, and manage Slack workspaces
    - Share posts, connect with professionals, and manage LinkedIn content

    Ask this agent to perform any task that requires integration with these platforms.
    Provide clear context about which platform you want to use and what specific actions you need.
    The agent will handle authentication and API interactions automatically.
    """,
    "inputs": {
        "task": {
            "type": "string",
            "description": "The task to be performed by the agent",
            "nullable": False,
        }
    },
}


parallel_agent_config = {
    "name": "parallel_agent",
    "description": """A versatile team member that specializes in extracting structured information from web pages, and the final results will be stored in an Excel file.

    Ensure that clear and specific information requirements are provided.
    For example: "extract the information of the dataset name, including name, Number of downloads, Number of like, download url"
    """,
    "inputs": {
        "task": {
            "type": "string",
            "description": "The task to be performed by the agent",
            "nullable": False,
        }
    },
}
