# AnotherMe Module

AnotherMe is a powerful AI agent system that serves as the core implementation of the project. It provides a multi-agent architecture capable of performing browser automation, web research, and code execution tasks.

## Usage

### Configure environment variables
Quick Start
```bash
touch .env
### using your keys
SERPAPI_API_KEY=...
SERPER_API_KEY=...
OPENAI_API_KEY=...
```

### Basic Usage

```python
from anotherme.run import create_agent

# Create an agent with default settings
agent = create_agent(model_id="gpt-4o")

# Run a task
result = agent.run("Search for information about climate change and summarize the findings")
```

### Scripts
The `run.py` script supports the following parameters:

- `question` (str, required): The task or question to be processed.
- `--model-id` (str, optional): Specifies the AI model to use. Default: `"gpt-4o"`.

Example usage:
```bash
python ./anotherme/run.py --model-id "o3-mini" "How many studio albums did Mercedes Sosa release before 2007?"
```
**NOTE:** You may need to add the environment path:

```bash
export PYTHONPATH="${PYTHONPATH}:path_to_anotherme/anotherme"
```
### Demo

A terminal-based interactive session for AnotherMe, supporting the following parameters:

- `--model_id` (str): Specifies the AI model version. Default: `"o3-mini"`.
- `--task_file` (str, optional): Path to markdown file containing predefined tasks. Default: `None`.

#### Examples

1. **Basic Interactive Mode**
   Start a conversational session (cookies optional for authentication):
   ```bash
   python ./anotherme/demo.py  # --cookies path/to/cookies (optional)
   ```

2. **Predefined Task Execution**
   Run tasks from a markdown file:
   ```bash
   python anotherme/demo.py --task_file /path/to/task_file.md
   ```

- Task File Format
Create markdown files with this structure:
  ```markdown
  [Task Name]
  [Task objective description]

  Steps
  1. [Action 1 description]
  2. [Action 2 description]
  ...
  ```
- Example Task File
  ``` markdown
  Scrape Amazon Products, Compare Prices on eBay, and Local Logging

  1. Open Amazon: https://www.amazon.com
  2. Search for user-configured keyword ("Wireless Earbuds"), extract first product's name, price, and rating
  3. Open eBay: https://www.ebay.com using extracted product name
  4. Extract top 3 eBay listings (name, price, seller rating)
  5. Save results to amazon.xlsx
  6. Populate row 1 with Amazon product data
  7. Populate rows 2-4 with eBay product data
  8. Calculate price differences and highlight lowest price source
  ```

#### Dev Test
1. **Test the multimodal understanding capabilities of BrowserUse for web pages.**
    ```bash
    python tests/test_browser_video.py
    ```
2. **Test the image search in 1688.**
    ```bash
    python tests/test_imagesearch_1688.py
    ```
    If you are not logged in to 1688, run the code below to log in:
    ```bash
    python helpers/save_user_data.py --url https://www.1688.com/
    ```
3. **Using the Paralle Agent to extract structure information from webpage.**
    ```bash
    python anotherme/parallel/parallel_agent.py
    ```

## Core Components

### Agent System

The agent system is built around a multi-agent architecture:

- **Manager Agent (CodeAgent)**: Coordinates the overall task execution, planning, and delegation to other agents.
- **Search Agent (ToolCallingAgent)**: Handles web searches and information retrieval via APIs.
- **Browser Agent (BrowserUseAgent)**: Performs browser automation tasks for web interaction.


### Tools

AnotherMe includes a variety of tools for different tasks:

- **Web Tools**:
  - `GoogleSearchTool`: Performs web searches via Serper API.
  - `SimpleTextBrowser`: Text-based browser for lightweight web navigation.
  - `VisitTool`, `PageDownTool`, `PageUpTool`: Navigation tools.
  - `FinderTool`, `FindNextTool`: Content search tools.
  - `ArchiveSearchTool`: Searches web archives.
  - `TextInspectorTool`: Analyzes text content.

- **Planning Tools**:
  - `PlanTool`: Helps agents create and manage execution plans.

- **Visual Tools**:
  - `visualizer`: Processes and analyzes images.

### Models

The system supports various LLM models through a flexible model interface:

- `LiteLLMModel`: Wrapper for LiteLLM-compatible models.
- Support for OpenAI models like GPT-4o.



## Architecture

The module follows a structured architecture:

- **agents.py**: Defines the core agent classes and their behaviors.
- **run.py**: Provides the main entry point for creating and running agents.
- **demo.py**: Provides the demo with cli.
- **browser_use/**: Contains the browser automation implementation.
- **tools/**: Implementation of various tools.
- **models/**: Model definitions and interfaces.
- **prompts/**: Prompt templates for different agents.
- **utils/**: Utility functions and helpers.


## License

See the project root for license information.
